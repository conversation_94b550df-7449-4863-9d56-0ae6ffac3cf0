import { nanoid } from "nanoid";
import { useContext } from "react";
import { useForm } from "react-hook-form";
import { Flip, toast } from "react-toastify";
import { ToDoContext } from "../Wrapper.jsx";

const Create = () => {
  const { todos, setTodos } = useContext(ToDoContext);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm();
  const submitHandler = (data) => {
    data.isCompleted = false;
    data.id = nanoid();
    setTodos([...todos, data]);
    toast.success("Todo added successfully!", {
      position: "top-center",
      autoClose: 5000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "dark",
      transition: Flip,
    });
    reset();
  };
  return (
    <>
      <form
        className="w-full flex items-center justify-between relative"
        onSubmit={handleSubmit(submitHandler)}
      >
        <input
          {...register("title", { required: "The title is required" })}
          className="w-full rounded-lg p-2 outline-none border-2"
          type="text"
          placeholder="Enter your ToDo"
        />
        <button className="bg-white text-black rounded-lg py-2 px-5 absolute right-0 cursor-pointer">
          Add
        </button>
      </form>
      {errors.title && (
        <p className="text-red-500 mt-1">{errors.title.message}</p>
      )}
    </>
  );
};

export default Create;
