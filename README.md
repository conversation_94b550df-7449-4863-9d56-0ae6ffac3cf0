# Todo App with React + Vite

A simple Todo application built with React and Vite that allows users to create, read, update, and delete todo items.

## Features

- Create new todo items
- View list of todos
- Edit existing todos
- Delete todos
- Toast notifications for user actions

## Technologies Used

- React 19
- Vite 6
- <PERSON><PERSON>windCSS 4
- React Hook Form for form handling
- React Toastify for notifications
- Nanoid for unique IDs

## Getting Started

### Prerequisites

- Node.js (version 18 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Start the development server:
   ```
   npm run dev
   ```

## Build for Production

```
npm run build
```

## Preview Production Build

```
npm run preview
```

## Project Structure

- `src/Components/Create.jsx` - Component for creating new todos
- `src/Components/Read.jsx` - Component for displaying and managing todos
- `src/App.jsx` - Main application component
- `src/main.jsx` - Application entry point

## License

This project is open source and available under the [MIT License](LICENSE).

