# Todo App with React + Vite

A simple Todo application built with React and Vite that allows users to create, read, update, and delete todo items. The application uses React Context for state management, providing a clean and efficient way to share todo data across components without prop drilling.

## Features

- Create new todo items
- View list of todos
- Edit existing todos (inline editing)
- Delete todos
- Toast notifications for user actions
- React Context for centralized state management
- No prop drilling - clean component architecture

## Technologies Used

- React 19 (with Context API for state management)
- Vite 6
- TailwindCSS 4
- React Hook Form for form handling
- React Toastify for notifications
- Nanoid for unique IDs

## Getting Started

### Prerequisites

- Node.js (version 18 or higher)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Start the development server:
   ```
   npm run dev
   ```

## Build for Production

```
npm run build
```

## Preview Production Build

```
npm run preview
```

## Project Structure

- `src/Components/Create.jsx` - Component for creating new todos (uses Context)
- `src/Components/Read.jsx` - Component for displaying and managing todos (uses Context)
- `src/App.jsx` - Main application component
- `src/Wrapper.jsx` - Context provider component that wraps the entire app
- `src/main.jsx` - Application entry point with Context provider setup

## Architecture

This application uses React Context API for state management:

- **ToDoContext**: Provides centralized state management for todos
- **Wrapper Component**: Context provider that wraps the entire application
- **Components**: Access shared state through `useContext` hook instead of props
- **Benefits**: Eliminates prop drilling, cleaner component hierarchy, easier state management

## License

This project is open source and available under the [MIT License](LICENSE).

