import { useContext, useState } from "react";
import { Flip, toast } from "react-toastify";
import { ToDoContext } from "../Wrapper.jsx";

const Read = () => {
  const [editingId, setEditingId] = useState(null);
  const [editText, setEditText] = useState("");
  const { todos, setTodos } = useContext(ToDoContext);

  const DeleteHandler = (id) => {
    const newTodos = todos.filter((todo) => todo.id !== id);
    setTodos(newTodos);
    toast.error("Todo deleted successfully!", {
      position: "top-center",
      autoClose: 5000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "dark",
      transition: Flip,
    });
  };

  const EditHandler = (id) => {
    if (editingId === id) {
      // Save the edited todo
      setTodos(
        todos.map((todo) =>
          todo.id === id ? { ...todo, title: editText } : todo
        )
      );
      setEditingId(null);
    } else {
      // Enter edit mode
      const todoToEdit = todos.find((todo) => todo.id === id);
      setEditText(todoToEdit.title);
      setEditingId(id);
    }
  };

  const readTodo = todos.map((todo) => {
    const isEditing = editingId === todo.id;

    return (
      <div
        key={todo.id}
        className="flex items-center justify-between my-2 bg-slate-700 px-3 py-2 rounded"
      >
        <li>
          <input
            type="text"
            value={isEditing ? editText : todo.title}
            disabled={!isEditing}
            onChange={(e) => setEditText(e.target.value)}
            autoFocus={isEditing}
            className={`w-full rounded-lg p-2 outline-none ${
              isEditing ? "border-2 border-white" : "border-none"
            }`}
          />
        </li>
        <div className="flex items-center gap-2">
          <button
            className="bg-green-500 text-white rounded-lg py-1 px-3 cursor-pointer"
            onClick={() => EditHandler(todo.id)}
          >
            {isEditing ? "Save" : "Edit"}
          </button>
          <button
            className="bg-red-500 text-white rounded-lg py-1 px-3 cursor-pointer"
            onClick={() => DeleteHandler(todo.id)}
          >
            Delete
          </button>
        </div>
      </div>
    );
  });

  return (
    <div className="w-full mt-4">
      <ul>{readTodo}</ul>
    </div>
  );
};

export default Read;
